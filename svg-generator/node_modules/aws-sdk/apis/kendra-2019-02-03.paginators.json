{"pagination": {"GetSnapshots": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListAccessControlConfigurations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListDataSourceSyncJobs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListDataSources": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListEntityPersonas": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListExperienceEntities": {"input_token": "NextToken", "output_token": "NextToken"}, "ListExperiences": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListFaqs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListGroupsOlderThanOrderingId": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListIndices": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListQuerySuggestionsBlockLists": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListThesauri": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}}}