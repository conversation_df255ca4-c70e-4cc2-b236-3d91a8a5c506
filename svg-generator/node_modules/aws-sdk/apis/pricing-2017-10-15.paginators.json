{"pagination": {"DescribeServices": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Services"}, "GetAttributeValues": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AttributeValues"}, "GetProducts": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "PriceList"}, "ListPriceLists": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "PriceLists"}}}